/** biome-ignore-all lint/nursery/useExportsLast: Redundant */

import { type BlobType, isPromise } from '../../shared'
import { OK_SYM } from '../const'

export const ok = <Type>(value: Type): Type extends std.Ok<BlobType> ? Type : std.Ok<Type> => {
  if (isOk(value)) {
    return value as BlobType
  }

  const result = {
    [OK_SYM]: value,

    [Symbol.for('nodejs.util.inspect.custom')]: () => value,

    toJSON: () => {
      return value
    },
    toString: () => {
      return value
    },
    // biome-ignore lint/correctness/useYield: Redundant
    *[Symbol.iterator]() {
      return this.value
    },
  } as BlobType

  if (isPromise(value)) {
    Object.assign(result, {
      async *[Symbol.asyncIterator]() {
        const result = await value

        if (result.isErr()) {
          yield result
        }

        return result[OK_SYM]
      },
    })
  }

  return result
}

export const isOk = (value: unknown): value is std.Ok<BlobType> => {
  return (value && typeof value === 'object' && OK_SYM in value) as boolean
}

const hi = ok(1)
