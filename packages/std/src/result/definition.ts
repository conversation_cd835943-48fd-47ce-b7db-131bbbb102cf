import type { EmptyType, Merge } from '../shared'
import type { ASYNC_SYM, OK_SYM } from './const'

declare global {
  export namespace std {
    export type Ok<Type> = Merge<
      {
        [OK_SYM]: Type
        [Symbol.iterator](): Generator<Type, Type, unknown>
      },
      Type extends PromiseLike<infer Type2>
        ? {
            [ASYNC_SYM]: true
            [Symbol.asyncIterator](): Generator<never, Type2, unknown>
          }
        : EmptyType
    >
  }
}
