{"type": "module", "version": "0.0.5", "name": "@ozaco/std", "sideEffects": ["./globals", "./result-global"], "bundleDependencies": ["picocolors", "type-fest"], "dependencies": {"type-fest": "^4.41.0"}, "devDependencies": {"@ozaco/cli": "workspace:*"}, "exports": {"./globals": {"default": "./dist/globals.js", "source": "./src/globals.ts", "types": "./dist/globals.d.ts"}, "./result": {"default": "./dist/result.js", "source": "./src/result/index.ts", "types": "./dist/result.d.ts"}, "./result-global": {"default": "./dist/result-global.js", "source": "./src/result/global.ts", "types": "./dist/result-global.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "author": "giveerr (https://github.com/giveerr)", "homepage": "https://ozaco.com/", "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}}